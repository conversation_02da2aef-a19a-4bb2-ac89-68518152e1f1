<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>Virtual <PERSON><PERSON> - <PERSON></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Animation Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/TextPlugin.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>

    <!-- Particles.js for background effects -->
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>

    <style>
        :root {
            --primary-color: #ff6b9d;
            --secondary-color: #4ecdc4;
            --accent-color: #45b7d1;
            --dark-bg: #0a0a0a;
            --darker-bg: #050505;
            --text-light: #ffffff;
            --text-glow: #ff6b9d;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --neon-glow: 0 0 20px rgba(255, 107, 157, 0.5);
            --box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: var(--dark-bg);
            color: var(--text-light);
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Particles Background */
        #particles-js {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 1;
        }

        /* Main Container */
        .game-container {
            position: relative;
            width: 100%;
            max-width: 480px;
            height: 100vh;
            margin: 0 auto;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
            overflow: hidden;
            z-index: 2;
            border-radius: 0;
        }

        @media (min-width: 768px) {
            .game-container {
                max-width: 520px;
                height: 95vh;
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                box-shadow: var(--box-shadow);
                margin-top: 2.5vh;
            }
        }

        /* Animated Background */
        .bg-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('assets/background/kota.png') center/cover no-repeat;
            filter: brightness(0.3) saturate(1.2);
            z-index: 1;
        }

        .bg-gradient {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                180deg,
                rgba(15, 23, 42, 0.1) 0%,
                rgba(15, 23, 42, 0.7) 60%,
                rgba(15, 23, 42, 0.95) 100%
            );
            z-index: 2;
        }

        /* Header Section */
        .header {
            position: relative;
            z-index: 10;
            text-align: center;
            padding: 60px 20px 40px;
        }

        .game-logo {
            font-family: 'Orbitron', monospace;
            font-size: 2.5rem;
            font-weight: 900;
            background: var(--gradient-secondary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: var(--neon-glow);
            margin-bottom: 10px;
            opacity: 0;
            transform: translateY(-50px);
        }

        .game-subtitle {
            font-size: 1.1rem;
            color: var(--secondary-color);
            font-weight: 300;
            letter-spacing: 2px;
            opacity: 0;
            transform: translateY(30px);
            margin-bottom: 20px;
        }

        /* Character Preview */
        .character-preview {
            position: relative;
            z-index: 5;
            height: 40vh;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            margin: 20px 0;
            opacity: 0;
        }

        /* Game Menu */
        .game-menu {
            position: relative;
            z-index: 10;
            padding: 0 30px 40px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }

        .start-game-text {
            font-family: 'Orbitron', monospace;
            font-size: 2rem;
            font-weight: 700;
            background: var(--gradient-secondary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            cursor: pointer;
            position: relative;
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-align: center;
            letter-spacing: 3px;
            text-shadow: 0 0 30px rgba(255, 107, 157, 0.5);
        }

        .start-game-text:hover {
            transform: translateY(-5px) scale(1.05);
            text-shadow:
                0 0 20px rgba(255, 107, 157, 0.8),
                0 0 40px rgba(255, 107, 157, 0.6),
                0 0 60px rgba(255, 107, 157, 0.4);
        }

        .start-game-text:active {
            transform: translateY(-2px) scale(0.98);
        }

        /* Animated underline */
        .start-game-text::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            width: 0;
            height: 3px;
            background: var(--gradient-secondary);
            transform: translateX(-50%);
            transition: width 0.4s ease;
            border-radius: 2px;
            box-shadow: 0 0 10px rgba(255, 107, 157, 0.5);
        }

        .start-game-text:hover::after {
            width: 100%;
        }

        /* Pulsing glow animation */
        @keyframes pulse-glow {
            0%, 100% {
                text-shadow:
                    0 0 20px rgba(255, 107, 157, 0.5),
                    0 0 30px rgba(255, 107, 157, 0.3);
            }
            50% {
                text-shadow:
                    0 0 30px rgba(255, 107, 157, 0.8),
                    0 0 50px rgba(255, 107, 157, 0.6),
                    0 0 70px rgba(255, 107, 157, 0.4);
            }
        }

        .start-game-text {
            animation: pulse-glow 3s ease-in-out infinite;
        }

        /* Story Loading Animation (only for navigation) */
        .story-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
            z-index: 3000;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            opacity: 0;
            pointer-events: none;
        }

        .story-loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(255, 107, 157, 0.3);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        .story-loading-text {
            font-size: 1.1rem;
            color: var(--primary-color);
            font-weight: 600;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Floating Elements */
        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            .game-logo {
                font-size: 2rem;
            }

            .game-subtitle {
                font-size: 1rem;
            }

            .start-game-text {
                font-size: 1.8rem;
                letter-spacing: 2px;
            }
        }

        @media (max-width: 375px) {
            .header {
                padding: 50px 15px 30px;
            }

            .game-menu {
                padding: 0 20px 30px;
            }

            .game-logo {
                font-size: 1.8rem;
            }

            .start-game-text {
                font-size: 1.6rem;
                letter-spacing: 1px;
            }
        }

        /* Safe area for notched devices */
        @supports (padding: max(0px)) {
            .header {
                padding-top: max(60px, env(safe-area-inset-top) + 20px);
            }

            .game-menu {
                padding-bottom: max(40px, env(safe-area-inset-bottom) + 20px);
            }
        }

        /* Sparkle effect */
        .sparkle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #fff;
            border-radius: 50%;
            animation: sparkle 1.5s ease-out forwards;
            pointer-events: none;
        }

        @keyframes sparkle {
            0% {
                opacity: 1;
                transform: scale(0) rotate(0deg);
            }
            50% {
                opacity: 1;
                transform: scale(1) rotate(180deg);
            }
            100% {
                opacity: 0;
                transform: scale(0) rotate(360deg);
            }
        }

        /* Hidden class for animations */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- Particles Background -->
    <div id="particles-js"></div>

    <!-- Story Loading Screen (only for navigation) -->
    <div class="story-loading-overlay" id="storyLoadingScreen">
        <div class="story-loading-spinner"></div>
        <div class="story-loading-text">Loading Story...</div>
    </div>

    <!-- Main Game Container -->
    <div class="game-container">
        <!-- Background Elements -->
        <div class="bg-overlay"></div>
        <div class="bg-gradient"></div>

        <!-- Floating Decorative Elements -->
        <div class="floating-element">💖</div>
        <div class="floating-element">✨</div>
        <div class="floating-element">🌸</div>

        <!-- Header Section -->
        <div class="header">
            <h1 class="game-logo" id="gameTitle">VIRTUAL</h1>
            <h2 class="game-subtitle" id="gameSubtitle">TEMAN WANITA</h2>
        </div>

        <!-- Game Menu -->
        <div class="game-menu" id="gameMenu">
            <div class="start-game-text" id="startGameText" onclick="startGame()">
                START GAME
            </div>
        </div>
    </div>

    <script>
        // Initialize particles.js
        particlesJS('particles-js', {
            particles: {
                number: {
                    value: 50,
                    density: {
                        enable: true,
                        value_area: 800
                    }
                },
                color: {
                    value: ['#ff6b9d', '#4ecdc4', '#45b7d1']
                },
                shape: {
                    type: 'circle',
                    stroke: {
                        width: 0,
                        color: '#000000'
                    }
                },
                opacity: {
                    value: 0.3,
                    random: true,
                    anim: {
                        enable: true,
                        speed: 1,
                        opacity_min: 0.1,
                        sync: false
                    }
                },
                size: {
                    value: 3,
                    random: true,
                    anim: {
                        enable: true,
                        speed: 2,
                        size_min: 0.1,
                        sync: false
                    }
                },
                line_linked: {
                    enable: true,
                    distance: 150,
                    color: '#ff6b9d',
                    opacity: 0.2,
                    width: 1
                },
                move: {
                    enable: true,
                    speed: 1,
                    direction: 'none',
                    random: false,
                    straight: false,
                    out_mode: 'out',
                    bounce: false,
                    attract: {
                        enable: false,
                        rotateX: 600,
                        rotateY: 1200
                    }
                }
            },
            interactivity: {
                detect_on: 'canvas',
                events: {
                    onhover: {
                        enable: true,
                        mode: 'repulse'
                    },
                    onclick: {
                        enable: true,
                        mode: 'push'
                    },
                    resize: true
                },
                modes: {
                    grab: {
                        distance: 140,
                        line_linked: {
                            opacity: 1
                        }
                    },
                    bubble: {
                        distance: 400,
                        size: 40,
                        duration: 2,
                        opacity: 8,
                        speed: 3
                    },
                    repulse: {
                        distance: 100,
                        duration: 0.4
                    },
                    push: {
                        particles_nb: 4
                    },
                    remove: {
                        particles_nb: 2
                    }
                }
            },
            retina_detect: true
        });

        // GSAP Animations
        document.addEventListener('DOMContentLoaded', function() {
            // Initial setup - hide elements
            gsap.set(['.game-logo', '.game-subtitle', '.start-game-text'], {
                opacity: 0
            });

            // Start intro animation immediately (no loading screen)
            startIntroAnimation();
        });

        function startIntroAnimation() {
            const tl = gsap.timeline();

            // Title animation with typewriter effect
            tl.to('.game-logo', {
                opacity: 1,
                y: 0,
                duration: 1,
                ease: 'back.out(1.7)'
            })
            .to('.game-subtitle', {
                opacity: 1,
                y: 0,
                duration: 0.8,
                ease: 'power2.out'
            }, '-=0.5')
            .to('.start-game-text', {
                opacity: 1,
                y: 0,
                duration: 0.8,
                ease: 'back.out(1.7)'
            }, '-=0.3');

            // Add glow effect to title
            gsap.to('.game-logo', {
                textShadow: '0 0 30px rgba(255, 107, 157, 0.8)',
                duration: 2,
                repeat: -1,
                yoyo: true,
                ease: 'power2.inOut'
            });
        }

        // Game Functions
        function startGame() {
            // Add click animation with sparkles
            animateTextClick(event.target);

            // Show loading and navigate to story
            showLoadingTransition(() => {
                window.location.href = 'story.php?char=qiara&scene=intro';
            });
        }

        // Utility Functions
        function animateTextClick(textElement) {
            // Scale animation
            gsap.to(textElement, {
                scale: 0.95,
                duration: 0.1,
                yoyo: true,
                repeat: 1,
                ease: 'power2.inOut'
            });

            // Create sparkle effects
            for (let i = 0; i < 8; i++) {
                setTimeout(() => {
                    createSparkle(textElement);
                }, i * 50);
            }

            // Enhanced glow effect
            gsap.to(textElement, {
                textShadow: '0 0 50px rgba(255, 107, 157, 1), 0 0 80px rgba(255, 107, 157, 0.8)',
                duration: 0.3,
                yoyo: true,
                repeat: 1,
                ease: 'power2.inOut'
            });
        }

        function createSparkle(element) {
            const sparkle = document.createElement('div');
            sparkle.className = 'sparkle';

            const rect = element.getBoundingClientRect();
            const x = Math.random() * rect.width;
            const y = Math.random() * rect.height;

            sparkle.style.left = x + 'px';
            sparkle.style.top = y + 'px';
            sparkle.style.background = `hsl(${Math.random() * 60 + 300}, 100%, 80%)`;

            element.appendChild(sparkle);

            setTimeout(() => {
                if (sparkle.parentNode) {
                    sparkle.parentNode.removeChild(sparkle);
                }
            }, 1500);
        }

        function showLoadingTransition(callback) {
            const loadingScreen = document.getElementById('storyLoadingScreen');
            loadingScreen.style.pointerEvents = 'all';

            gsap.to(loadingScreen, {
                opacity: 1,
                duration: 0.5,
                onComplete: () => {
                    setTimeout(callback, 1500);
                }
            });
        }

        // Add touch feedback for mobile
        document.getElementById('startGameText').addEventListener('touchstart', function() {
            gsap.to(this, {
                scale: 0.95,
                duration: 0.1
            });
        });

        document.getElementById('startGameText').addEventListener('touchend', function() {
            gsap.to(this, {
                scale: 1,
                duration: 0.1
            });
        });

        // Preload story page for faster navigation
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = 'story.php';
        document.head.appendChild(link);

        // Add mouse trail effect for desktop
        document.addEventListener('mousemove', function(e) {
            if (Math.random() < 0.1) { // Only create particles occasionally
                createMouseTrail(e.clientX, e.clientY);
            }
        });

        function createMouseTrail(x, y) {
            const trail = document.createElement('div');
            trail.style.cssText = `
                position: fixed;
                left: ${x}px;
                top: ${y}px;
                width: 3px;
                height: 3px;
                background: radial-gradient(circle, #ff6b9d, transparent);
                border-radius: 50%;
                pointer-events: none;
                z-index: 1;
                animation: particle-fade 1s ease-out forwards;
            `;

            document.body.appendChild(trail);

            setTimeout(() => {
                if (trail.parentNode) {
                    trail.parentNode.removeChild(trail);
                }
            }, 1000);
        }

        // Add particle trail animation CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes particle-fade {
                0% {
                    opacity: 1;
                    transform: scale(1);
                }
                100% {
                    opacity: 0;
                    transform: scale(0) translateY(-30px);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
/* Paradise Garden CSS Background */
.paradise-garden {
  background: linear-gradient(
    135deg,
    #87CEEB 0%,     /* Sky blue */
    #98FB98 25%,    /* <PERSON>le green */
    #90EE90 50%,    /* Light green */
    #32CD32 75%,    /* Lime green */
    #228B22 100%    /* Forest green */
  );
  position: relative;
  overflow: hidden;
}

.paradise-garden::before {
  content: '';
  position: absolute;
  inset: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(255,182,193,0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255,255,0,0.2) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255,20,147,0.2) 0%, transparent 30%);
}

.paradise-garden::after {
  content: '';
  position: absolute;
  inset: 0;
  background: 
    linear-gradient(45deg, transparent 40%, rgba(255,255,255,0.1) 50%, transparent 60%),
    linear-gradient(-45deg, transparent 40%, rgba(255,255,255,0.1) 50%, transparent 60%);
}

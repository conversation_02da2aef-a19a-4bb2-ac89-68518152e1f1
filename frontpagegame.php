<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>Virtual <PERSON><PERSON> - <PERSON></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Animation Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/TextPlugin.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>

    <!-- Particles.js for background effects -->
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>

    <style>
        :root {
            --primary-color: #ff6b9d;
            --secondary-color: #4ecdc4;
            --accent-color: #45b7d1;
            --dark-bg: #0a0a0a;
            --darker-bg: #050505;
            --text-light: #ffffff;
            --text-glow: #ff6b9d;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --neon-glow: 0 0 20px rgba(255, 107, 157, 0.5);
            --box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: var(--dark-bg);
            color: var(--text-light);
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Particles Background */
        #particles-js {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 1;
        }

        /* Main Container */
        .game-container {
            position: relative;
            width: 100%;
            max-width: 480px;
            height: 100vh;
            margin: 0 auto;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
            overflow: hidden;
            z-index: 2;
            border-radius: 0;
        }

        @media (min-width: 768px) {
            .game-container {
                max-width: 520px;
                height: 95vh;
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                box-shadow: var(--box-shadow);
                margin-top: 2.5vh;
            }
        }

        /* Animated Background */
        .bg-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('assets/background/kota.png') center/cover no-repeat;
            filter: brightness(0.3) saturate(1.2);
            z-index: 1;
        }

        .bg-gradient {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                180deg,
                rgba(15, 23, 42, 0.1) 0%,
                rgba(15, 23, 42, 0.7) 60%,
                rgba(15, 23, 42, 0.95) 100%
            );
            z-index: 2;
        }

        /* Header Section */
        .header {
            position: relative;
            z-index: 10;
            text-align: center;
            padding: 60px 20px 40px;
        }

        .game-logo {
            font-family: 'Orbitron', monospace;
            font-size: 2.5rem;
            font-weight: 900;
            background: var(--gradient-secondary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: var(--neon-glow);
            margin-bottom: 10px;
            opacity: 0;
            transform: translateY(-50px);
        }

        .game-subtitle {
            font-size: 1.1rem;
            color: var(--secondary-color);
            font-weight: 300;
            letter-spacing: 2px;
            opacity: 0;
            transform: translateY(30px);
            margin-bottom: 20px;
        }

        /* Character Preview */
        .character-preview {
            position: relative;
            z-index: 5;
            height: 40vh;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            margin: 20px 0;
            opacity: 0;
        }

        /* Game Menu */
        .game-menu {
            position: relative;
            z-index: 10;
            padding: 0 30px 40px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .menu-button {
            background: var(--gradient-primary);
            border: none;
            padding: 18px 30px;
            border-radius: 50px;
            color: var(--text-light);
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            opacity: 0;
            transform: translateX(-100px);
        }

        .menu-button:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .menu-button:active {
            transform: translateY(-1px) scale(0.98);
        }

        .menu-button.primary {
            background: var(--gradient-secondary);
            box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
            font-size: 1.2rem;
            padding: 20px 35px;
        }

        .menu-button.primary:hover {
            box-shadow: 0 12px 35px rgba(255, 107, 157, 0.5);
        }

        /* Ripple Effect */
        .menu-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .menu-button:active::before {
            width: 300px;
            height: 300px;
        }

        /* Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--darker-bg);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(255, 107, 157, 0.3);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        .loading-text {
            font-size: 1.1rem;
            color: var(--primary-color);
            font-weight: 600;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Floating Elements */
        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            .game-logo {
                font-size: 2rem;
            }

            .game-subtitle {
                font-size: 1rem;
            }

            .menu-button {
                padding: 16px 25px;
                font-size: 1rem;
            }

            .menu-button.primary {
                padding: 18px 30px;
                font-size: 1.1rem;
            }

            .character-preview {
                height: 35vh;
            }
        }

        @media (max-width: 375px) {
            .header {
                padding: 50px 15px 30px;
            }

            .game-menu {
                padding: 0 20px 30px;
            }

            .game-logo {
                font-size: 1.8rem;
            }
        }

        /* Safe area for notched devices */
        @supports (padding: max(0px)) {
            .header {
                padding-top: max(60px, env(safe-area-inset-top) + 20px);
            }

            .game-menu {
                padding-bottom: max(40px, env(safe-area-inset-bottom) + 20px);
            }
        }

        /* Hidden class for animations */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-overlay" id="loadingScreen">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading Virtual Teman Wanita...</div>
    </div>

    <!-- Particles Background -->
    <div id="particles-js"></div>

    <!-- Main Game Container -->
    <div class="game-container">
        <!-- Background Elements -->
        <div class="bg-overlay"></div>
        <div class="bg-gradient"></div>

        <!-- Floating Decorative Elements -->
        <div class="floating-element">💖</div>
        <div class="floating-element">✨</div>
        <div class="floating-element">🌸</div>

        <!-- Header Section -->
        <div class="header">
            <h1 class="game-logo" id="gameTitle">VIRTUAL</h1>
            <h2 class="game-subtitle" id="gameSubtitle">TEMAN WANITA</h2>
        </div>

        <!-- Character Preview -->
        <div class="character-preview" id="characterPreview">
            <img src="assets/characters/qiara/qiara1 (1).png" alt="Character" class="character-image" id="characterImage">
        </div>

        <!-- Game Menu -->
        <div class="game-menu" id="gameMenu">
            <button class="menu-button primary" id="startGameBtn" onclick="startGame()">
                🎮 START GAME
            </button>
            <button class="menu-button" id="continueBtn" onclick="continueGame()">
                📖 CONTINUE STORY
            </button>
            <button class="menu-button" id="settingsBtn" onclick="showSettings()">
                ⚙️ SETTINGS
            </button>
            <button class="menu-button" id="aboutBtn" onclick="showAbout()">
                ℹ️ ABOUT
            </button>
        </div>
    </div>

    <script>
        // Initialize particles.js
        particlesJS('particles-js', {
            particles: {
                number: {
                    value: 50,
                    density: {
                        enable: true,
                        value_area: 800
                    }
                },
                color: {
                    value: ['#ff6b9d', '#4ecdc4', '#45b7d1']
                },
                shape: {
                    type: 'circle',
                    stroke: {
                        width: 0,
                        color: '#000000'
                    }
                },
                opacity: {
                    value: 0.3,
                    random: true,
                    anim: {
                        enable: true,
                        speed: 1,
                        opacity_min: 0.1,
                        sync: false
                    }
                },
                size: {
                    value: 3,
                    random: true,
                    anim: {
                        enable: true,
                        speed: 2,
                        size_min: 0.1,
                        sync: false
                    }
                },
                line_linked: {
                    enable: true,
                    distance: 150,
                    color: '#ff6b9d',
                    opacity: 0.2,
                    width: 1
                },
                move: {
                    enable: true,
                    speed: 1,
                    direction: 'none',
                    random: false,
                    straight: false,
                    out_mode: 'out',
                    bounce: false,
                    attract: {
                        enable: false,
                        rotateX: 600,
                        rotateY: 1200
                    }
                }
            },
            interactivity: {
                detect_on: 'canvas',
                events: {
                    onhover: {
                        enable: true,
                        mode: 'repulse'
                    },
                    onclick: {
                        enable: true,
                        mode: 'push'
                    },
                    resize: true
                },
                modes: {
                    grab: {
                        distance: 140,
                        line_linked: {
                            opacity: 1
                        }
                    },
                    bubble: {
                        distance: 400,
                        size: 40,
                        duration: 2,
                        opacity: 8,
                        speed: 3
                    },
                    repulse: {
                        distance: 100,
                        duration: 0.4
                    },
                    push: {
                        particles_nb: 4
                    },
                    remove: {
                        particles_nb: 2
                    }
                }
            },
            retina_detect: true
        });

        // GSAP Animations
        document.addEventListener('DOMContentLoaded', function() {
            // Initial setup - hide elements
            gsap.set(['.game-logo', '.game-subtitle', '.character-preview', '.menu-button'], {
                opacity: 0
            });

            // Loading animation
            setTimeout(() => {
                gsap.to('#loadingScreen', {
                    opacity: 0,
                    duration: 0.5,
                    onComplete: () => {
                        document.getElementById('loadingScreen').style.display = 'none';
                        startIntroAnimation();
                    }
                });
            }, 2000);
        });

        function startIntroAnimation() {
            const tl = gsap.timeline();

            // Title animation with typewriter effect
            tl.to('.game-logo', {
                opacity: 1,
                y: 0,
                duration: 1,
                ease: 'back.out(1.7)'
            })
            .to('.game-subtitle', {
                opacity: 1,
                y: 0,
                duration: 0.8,
                ease: 'power2.out'
            }, '-=0.5')
            .to('.character-preview', {
                opacity: 1,
                duration: 1,
                ease: 'power2.out'
            }, '-=0.3')
            .to('.character-image', {
                scale: 1,
                duration: 1.2,
                ease: 'elastic.out(1, 0.5)'
            }, '-=0.8')
            .to('.menu-button', {
                opacity: 1,
                x: 0,
                duration: 0.6,
                stagger: 0.1,
                ease: 'back.out(1.7)'
            }, '-=0.5');

            // Add floating animation to character
            gsap.to('.character-image', {
                y: -10,
                duration: 2,
                repeat: -1,
                yoyo: true,
                ease: 'power2.inOut'
            });

            // Add glow effect to title
            gsap.to('.game-logo', {
                textShadow: '0 0 30px rgba(255, 107, 157, 0.8)',
                duration: 2,
                repeat: -1,
                yoyo: true,
                ease: 'power2.inOut'
            });
        }

        // Game Functions
        function startGame() {
            // Add click animation
            animateButtonClick(event.target);

            // Show loading and navigate to story
            showLoadingTransition(() => {
                window.location.href = 'story.php?char=qiara&scene=intro';
            });
        }

        function continueGame() {
            animateButtonClick(event.target);

            // Check for saved progress (you can implement localStorage logic here)
            const savedProgress = localStorage.getItem('gameProgress');
            if (savedProgress) {
                const progress = JSON.parse(savedProgress);
                showLoadingTransition(() => {
                    window.location.href = `story.php?char=${progress.char}&scene=${progress.scene}`;
                });
            } else {
                // No saved progress, start new game
                startGame();
            }
        }

        function showSettings() {
            animateButtonClick(event.target);

            // Create settings modal (you can expand this)
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 2000;
            `;

            modal.innerHTML = `
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: 30px;
                    border-radius: 20px;
                    text-align: center;
                    max-width: 300px;
                    width: 90%;
                ">
                    <h3 style="margin-bottom: 20px; color: white;">Settings</h3>
                    <p style="margin-bottom: 20px; color: white;">Settings panel coming soon!</p>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        background: rgba(255, 255, 255, 0.2);
                        border: none;
                        padding: 10px 20px;
                        border-radius: 10px;
                        color: white;
                        cursor: pointer;
                    ">Close</button>
                </div>
            `;

            document.body.appendChild(modal);

            // Animate modal
            gsap.fromTo(modal,
                { opacity: 0 },
                { opacity: 1, duration: 0.3 }
            );
            gsap.fromTo(modal.firstElementChild,
                { scale: 0.5, y: 50 },
                { scale: 1, y: 0, duration: 0.5, ease: 'back.out(1.7)' }
            );
        }

        function showAbout() {
            animateButtonClick(event.target);

            // Create about modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 2000;
            `;

            modal.innerHTML = `
                <div style="
                    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                    padding: 30px;
                    border-radius: 20px;
                    text-align: center;
                    max-width: 350px;
                    width: 90%;
                ">
                    <h3 style="margin-bottom: 15px; color: white;">About Virtual Teman Wanita</h3>
                    <p style="margin-bottom: 15px; color: white; font-size: 14px; line-height: 1.5;">
                        An interactive visual novel experience where you can build relationships with virtual characters through engaging storylines and meaningful choices.
                    </p>
                    <p style="margin-bottom: 20px; color: rgba(255,255,255,0.8); font-size: 12px;">
                        Version 1.0 - Created with ❤️
                    </p>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        background: rgba(255, 255, 255, 0.2);
                        border: none;
                        padding: 10px 20px;
                        border-radius: 10px;
                        color: white;
                        cursor: pointer;
                    ">Close</button>
                </div>
            `;

            document.body.appendChild(modal);

            // Animate modal
            gsap.fromTo(modal,
                { opacity: 0 },
                { opacity: 1, duration: 0.3 }
            );
            gsap.fromTo(modal.firstElementChild,
                { scale: 0.5, y: 50 },
                { scale: 1, y: 0, duration: 0.5, ease: 'back.out(1.7)' }
            );
        }

        // Utility Functions
        function animateButtonClick(button) {
            gsap.to(button, {
                scale: 0.95,
                duration: 0.1,
                yoyo: true,
                repeat: 1,
                ease: 'power2.inOut'
            });

            // Create ripple effect
            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;

            const rect = button.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = (rect.width / 2 - size / 2) + 'px';
            ripple.style.top = (rect.height / 2 - size / 2) + 'px';

            button.appendChild(ripple);

            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.parentNode.removeChild(ripple);
                }
            }, 600);
        }

        function showLoadingTransition(callback) {
            const loadingOverlay = document.createElement('div');
            loadingOverlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 3000;
                opacity: 0;
            `;

            loadingOverlay.innerHTML = `
                <div style="
                    width: 60px;
                    height: 60px;
                    border: 3px solid rgba(255, 107, 157, 0.3);
                    border-top: 3px solid #ff6b9d;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin-bottom: 20px;
                "></div>
                <div style="
                    font-size: 1.1rem;
                    color: #ff6b9d;
                    font-weight: 600;
                ">Loading Story...</div>
            `;

            document.body.appendChild(loadingOverlay);

            gsap.to(loadingOverlay, {
                opacity: 1,
                duration: 0.5,
                onComplete: () => {
                    setTimeout(callback, 1500);
                }
            });
        }

        // Add CSS for ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Character image hover effect
        document.getElementById('characterImage').addEventListener('mouseenter', function() {
            gsap.to(this, {
                scale: 1.05,
                duration: 0.3,
                ease: 'power2.out'
            });
        });

        document.getElementById('characterImage').addEventListener('mouseleave', function() {
            gsap.to(this, {
                scale: 1,
                duration: 0.3,
                ease: 'power2.out'
            });
        });

        // Add touch feedback for mobile
        document.querySelectorAll('.menu-button').forEach(button => {
            button.addEventListener('touchstart', function() {
                gsap.to(this, {
                    scale: 0.95,
                    duration: 0.1
                });
            });

            button.addEventListener('touchend', function() {
                gsap.to(this, {
                    scale: 1,
                    duration: 0.1
                });
            });
        });

        // Preload story page for faster navigation
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = 'story.php';
        document.head.appendChild(link);

        // Save game progress (basic implementation)
        function saveProgress(char, scene) {
            const progress = {
                char: char,
                scene: scene,
                timestamp: Date.now()
            };
            localStorage.setItem('gameProgress', JSON.stringify(progress));
        }

        // Check if there's saved progress and show continue button accordingly
        window.addEventListener('load', function() {
            const savedProgress = localStorage.getItem('gameProgress');
            const continueBtn = document.getElementById('continueBtn');

            if (!savedProgress) {
                continueBtn.style.opacity = '0.6';
                continueBtn.innerHTML = '🎮 NEW GAME';
            }
        });
    </script>
</body>
</html>